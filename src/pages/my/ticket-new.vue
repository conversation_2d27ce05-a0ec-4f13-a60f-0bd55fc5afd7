<template>
  <custom-tabs :model-value="current" @change="tabChange">
    <custom-tab-pane v-for="item in tabList" :id="item.type" :key="item.type" :label="item.label"
      :number="item.type === '1' ? canUseNumber : 0">
      <view v-if="computedId !== ''" class="card-page">
        <!-- 会员凭证页面 -->
        <view v-if="computedId === '1'" style="height: 100%">
          <ticket :from="newOptions.from" />
        </view>

        <!-- 商品凭证页面 -->
        <view v-if="computedId === '2'">
          <!-- 筛选按钮 -->
          <view class="filter-tabs">
            <view v-for="filter in filterList" :key="filter.value"
              :class="['filter-tab', { active: currentFilter === filter.value }]"
              @tap="handleFilterChange(filter.value)">
              {{ filter.label }}
            </view>
          </view>

          <!-- 商品凭证列表 -->
          <view class="voucher-list">
            <z-paging ref="ticketPaging" v-model="voucherList" :fixed="false" :refresher-enabled="false"
              :show-loading-more-no-more-view="voucherList.length > 10 ? true : false"
              :empty-view-img-style="{ width: '103rpx', height: '144rpx' }" empty-view-text="暂无核销数据"
              empty-view-img="https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png">
              <view v-for="voucher in voucherList" :key="voucher.consumption_log_id" class="voucher-item">
                <view class="voucher-content">
                  <image :src="voucher.commodity_img" class="voucher-image" mode="aspectFill" />
                  <view class="voucher-info">
                    <view class="voucher-title">{{ voucher.commodity_name }}</view>
                    <view class="voucher-quantity">x{{ voucher.purchase_count }} {{ voucher.unit }}</view>
                    <view class="voucher-date">{{ voucher.create_time }}</view>
                    <view class="voucher-store">门店：{{ voucher.bus_name }}</view>
                  </view>
                </view>
                <view class="voucher-action">
                  <view class="action-btn" @tap="handleViewVoucher(voucher)">
                    查看商品凭证 >
                  </view>
                </view>
              </view>
            </z-paging>
          </view>
        </view>
      </view>
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import ticket from './ticket.vue'

const tabList = ref([
  { type: '1', label: '入场凭证' },
  { type: '2', label: '商品凭证' },
])

const filterList = ref([
  { value: '0', label: '待核销' },
  { value: '1', label: '已核销' },
])

const computedId = ref('')
const { checkLogin } = useLogin()
const current = ref(0)
const currentFilter = ref('0')
const userStore = useUserStore()

onShow(() => {
  checkLogin()
})

const canUseNumber = ref(0)

const newOptions = ref()
onLoad((options) => {
  newOptions.value = options
})

function tabChange(e: any) {
  current.value = e.value
  computedId.value = e.computedId

  if (computedId.value === '2') {
    getVoucherList()
  }
}

function handleFilterChange(filterValue: string) {
  currentFilter.value = filterValue
  getVoucherList()
}

const ticketPaging = ref()
const voucherList = ref<any[]>([])
function getVoucherList() {
  http
    .get('/Good/getVoucherList', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      consume_status: currentFilter.value,
    })
    .then(async (res) => {
      console.log(ticketPaging.value)
      if (!ticketPaging.value) {
        await nextTick()
      }

      ticketPaging.value.reload()
      ticketPaging.value.setLocalPaging(res.data.list)
    })
    .catch(async () => {
      if (!ticketPaging.value) {
        await nextTick()
      }

      ticketPaging.value.setLocalPaging([], false)
    })
}

function handleViewVoucher(voucher: any) {
  // 处理查看商品凭证
  uni.showToast({
    title: `查看${voucher.commodity_name}的凭证`,
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.card-page {
  padding-top: 20rpx;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.normal-btn-min {
  width: 500rpx;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;

  &.center {
    justify-content: center;
  }
}

.text-ellipsis {
  display: inline-block;
  margin: 10rpx;
  max-width: 600rpx;
  width: 600rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 筛选标签样式
.filter-tabs {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  background-color: #f5f7f9;
  color: #666;
  transition: all 0.3s ease;

  &.active {
    background-color: #a1ea2b;
    color: #fff;
    font-weight: bold;
  }
}

// 商品凭证列表样式
.voucher-list {
  padding: 0 30rpx;
  height: 100%;
}

.voucher-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.voucher-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.voucher-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.voucher-info {
  flex: 1;
  min-width: 0;
}

.voucher-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.voucher-quantity {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  text-align: right;
}

.voucher-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.voucher-store {
  font-size: 24rpx;
  color: #999;
}

.voucher-action {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
</style>